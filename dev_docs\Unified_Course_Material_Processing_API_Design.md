# 统一课程材料处理 API 设计方案

## 1. 项目概述

### 1.1 背景需求

当用户上传课程材料时，系统需要自动完成以下处理流程：

1. 文件上传到 `data/uploads/course_id/` 目录
2. 使用 outline_service 生成大纲 MD 文件到 `data/outputs/outlines/` 目录
3. 将文档分块并建立 RAG 向量索引存储到 Qdrant 数据库
4. 为用户提供清晰的处理进度反馈
5. 支持失败时一键清理所有相关数据

### 1.2 设计目标

- **一站式服务**：单个 API 调用完成所有处理步骤
- **顺序执行**：确保先生成大纲再建立 RAG 索引，避免服务器压力过大
- **实时反馈**：用户能够看到 AI 处理的每个步骤和进度
- **完整清理**：支持删除特定 course_material_id 的所有痕迹
- **错误恢复**：处理失败时自动回滚已完成的操作

## 2. API 架构设计

### 2.1 核心 API 端点

#### 主处理 API

- **端点**：`POST /api/v1/course-materials/process`
- **功能**：统一处理课程材料上传、大纲生成和 RAG 索引建立
- **特点**：同步处理，实时返回结果

#### 状态查询 API

- **端点**：`GET /api/v1/course-materials/tasks/{task_id}/status`
- **功能**：查询处理任务的实时状态和进度

#### 清理 API

- **端点**：`DELETE /api/v1/course-materials/{course_id}/{course_material_id}`
- **功能**：删除指定课程材料的所有痕迹

#### 批量清理 API

- **端点**：`DELETE /api/v1/course-materials/{course_id}`
- **功能**：删除整个课程的所有材料

### 2.2 处理流程设计

#### 第一阶段：文件上传验证

1. 验证文件格式（仅支持.md 和.txt）
2. 检查 course_material_id 唯一性
3. 验证文件大小限制
4. 保存文件到指定路径

#### 第二阶段：大纲生成

1. 读取上传文件内容
2. 调用 outline_service 生成大纲
3. 保存大纲文件到 outputs 目录
4. 更新处理状态

#### 第三阶段：RAG 索引建立

1. 使用相同文件内容进行文本分块
2. 生成向量嵌入
3. 存储到 Qdrant 数据库
4. 记录索引元数据

#### 第四阶段：完成确认

1. 验证所有步骤完成状态
2. 返回最终处理结果
3. 记录处理日志

## 3. 数据模型设计

### 3.1 处理状态枚举

- `UPLOADING`：文件上传中
- `OUTLINE_GENERATING`：大纲生成中
- `RAG_INDEXING`：RAG 索引建立中
- `COMPLETED`：处理完成
- `FAILED`：处理失败

### 3.2 响应数据结构

包含以下核心字段：

- 任务标识信息（task_id、状态、消息）
- 进度信息（当前步骤、已完成步骤、总步骤数）
- 文件信息（课程 ID、材料 ID、文件名、大小）
- 处理结果（文件路径、大纲路径、RAG 索引状态）
- 错误信息（错误步骤、错误消息）
- 时间信息（处理时间、创建时间、完成时间）

### 3.3 清理结果数据结构

包含清理操作的详细结果：

- 清理成功状态
- 清理操作列表
- 每个操作的执行结果
- 清理统计信息

## 4. 错误处理机制

### 4.1 分步骤错误处理

- **上传阶段错误**：文件验证失败、存储空间不足
- **大纲生成错误**：API 调用失败、内容解析错误
- **RAG 索引错误**：向量生成失败、数据库连接问题

### 4.2 自动回滚机制

当任何步骤失败时：

1. 记录失败的具体步骤和原因
2. 自动清理已完成步骤产生的文件和数据
3. 返回详细的错误信息给用户
4. 提供手动清理选项

### 4.3 部分失败处理

支持用户选择：

- 保留已成功的部分（如大纲生成成功但 RAG 失败）
- 完全清理重新开始
- 从失败步骤继续处理

## 5. 统一清理 API 设计

### 5.1 清理范围定义

针对特定 course_material_id 的完整清理包括：

#### 文件系统清理

- 上传文件：`data/uploads/{course_id}/{course_material_id}_{material_name}.{ext}`
- 大纲文件：`data/outputs/outlines/{course_id}/{course_material_id}_{material_name}.md`
- 临时文件：`data/tmp/` 目录下相关临时文件

#### 数据库清理

- Qdrant 向量数据：删除所有 payload 中 course_material_id 匹配的向量点
- 任务记录：清理内存中的任务状态记录
- 日志记录：标记相关日志为已清理状态

#### 目录清理

- 检查并清理空的课程目录
- 维护目录结构的整洁性

### 5.2 清理 API 开发步骤

#### 步骤 1：设计清理服务类

创建专门的清理服务类，负责协调各种清理操作：

- 文件系统清理器
- Qdrant 数据清理器
- 任务状态清理器
- 目录维护器

#### 步骤 2：实现分层清理逻辑

按照依赖关系设计清理顺序：

1. 首先清理数据库中的向量数据
2. 然后删除文件系统中的文件
3. 最后清理内存中的状态信息
4. 维护目录结构

#### 步骤 3：添加安全检查机制

- 验证 course_material_id 的有效性
- 检查用户权限（如果有权限系统）
- 确认清理操作的影响范围
- 提供清理预览功能

#### 步骤 4：实现清理结果反馈

- 详细记录每个清理操作的结果
- 统计清理的文件数量和大小
- 报告清理过程中的任何错误
- 提供清理完成确认

#### 步骤 5：集成到主处理流程

- 在处理失败时自动调用清理服务
- 在用户手动删除时提供清理接口
- 支持批量清理操作
- 添加清理操作的审计日志

## 6. 用户体验设计

### 6.1 进度反馈机制

- **实时状态更新**：显示当前处理步骤和进度百分比
- **预估时间**：根据文件大小和历史数据预估处理时间
- **步骤可视化**：清晰展示处理流程的各个阶段
- **错误提示**：友好的错误信息和解决建议

### 6.2 交互设计原则

- **一键操作**：用户只需一次上传即可完成所有处理
- **透明处理**：用户能够看到 AI 处理的每个步骤
- **灵活控制**：支持选择性执行某些步骤
- **快速恢复**：失败时提供快速清理和重试选项

### 6.3 反馈信息设计

- **成功反馈**：明确告知用户处理完成和结果位置
- **进度反馈**：实时显示处理进度和当前操作
- **错误反馈**：清晰的错误信息和建议操作
- **清理反馈**：详细的清理结果和影响范围

## 7. 技术实现要点

### 7.1 服务集成

- **outline_service 集成**：复用现有大纲生成服务
- **RAG 服务集成**：复用现有 RAG 索引建立服务
- **文件服务集成**：复用现有文件上传和管理服务
- **Qdrant 集成**：复用现有向量数据库操作

### 7.2 性能优化

- **顺序执行**：避免并发处理造成服务器压力
- **资源管理**：合理控制内存和 CPU 使用
- **缓存策略**：缓存常用的配置和模板
- **连接池**：优化数据库连接管理

### 7.3 监控和日志

- **处理监控**：监控每个步骤的执行时间和成功率
- **错误监控**：及时发现和报告处理错误
- **性能监控**：跟踪 API 响应时间和资源使用
- **业务监控**：统计处理量和用户使用情况

## 8. 部署和配置

### 8.1 环境要求

- **Qdrant 服务**：数据持久化，用 fastapi 和 qdrant 进行数据交互，用 6334（gRPC）即可。
- **OpenAI API**：配置正确的 API 密钥和基础 URL
- **文件系统**：确保有足够的存储空间
- **内存配置**：根据并发需求调整内存分配

### 8.2 配置参数

- **处理超时时间**：设置合理的超时限制
- **文件大小限制**：配置最大文件大小
- **并发处理数**：控制同时处理的任务数量
- **清理策略**：配置自动清理的触发条件
